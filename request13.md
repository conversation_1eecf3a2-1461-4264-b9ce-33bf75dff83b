<PERSON><PERSON> tối ưu hóa tệp `home_screen.dart` theo mẫu thiết kế trong tệp `sceen_mau.pdf`, chúng ta cần điều chỉnh giao diện và logic để đáp ứng các yêu cầu sau:

- **Mặc định**: Giao diện hiển thị như trang 1 của `sceen_mau.pdf`, tức là chỉ hiển thị danh sách dự án và các nút menu chính, không có nội dung widget nào bên dưới.
- **Khi nhấn nút menu**: Nộ<PERSON> dung tương ứng (ví dụ: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> mớ<PERSON>, <PERSON><PERSON><PERSON> kh<PERSON><PERSON> l<PERSON>) sẽ hiển thị trong một panel có thể vuốt xuống để ẩn, giống như trang 2 của `sceen_mau.pdf`.

<PERSON><PERSON><PERSON> tại, mã trong `home_screen.dart` hiển thị nội dung trực tiếp trong một `Expanded` widget ngay dưới các nút menu, đi<PERSON>u này không phù hợp với yêu cầu. Dưới đây là cách phân tích và cải tiến mã để đạt được mục tiêu.

---

### Phân tích mã hiện tại trong `home_screen.dart`

1. **Cấu trúc giao diện**:
   - `Scaffold` chứa một `Column` với các thành phần:
     - `_buildHeader()`: Tiêu đề ứng dụng.
     - `_buildProjectList()`: Danh sách dự án dạng `PageView`.
     - `_buildPageIndicator()`: Chỉ báo trang.
     - `_buildMenuOptions()`: 3 nút menu (Thư viện, Thêm mới, Tính khối lượng).
     - `Expanded(child: _buildSelectedContent())`: Nội dung tương ứng với nút menu được chọn, luôn hiển thị.
   - Vấn đề: Nội dung `_buildSelectedContent()` được hiển thị cố định trong `Expanded`, không thể ẩn hoặc vuốt xuống như yêu cầu.

2. **Logic xử lý menu**:
   - Trong `_buildMenuOptions()`, mỗi nút gọi `setState()` để cập nhật `_selectedMenuIndex`, sau đó `_buildSelectedContent()` hiển thị nội dung tương ứng.
   - Không có cơ chế để ẩn nội dung hoặc hiển thị dưới dạng panel có thể vuốt.

3. **Hạn chế**:
   - Giao diện mặc định không giống trang 1 của `sceen_mau.pdf` vì luôn có nội dung hiển thị bên dưới menu.
   - Không có tính năng vuốt xuống để ẩn nội dung như trang 2 của `sceen_mau.pdf`.

---

### Giải pháp tối ưu hóa

Để đáp ứng yêu cầu, chúng ta sẽ:
1. **Loại bỏ nội dung cố định**: Xóa phần `Expanded` chứa `_buildSelectedContent()` khỏi giao diện mặc định.
2. **Sử dụng `ModalBottomSheet` với `DraggableScrollableSheet`**: Khi nhấn nút menu, hiển thị nội dung trong một bottom sheet có thể vuốt lên/xuống để điều chỉnh kích thước hoặc ẩn hoàn toàn.
3. **Điều chỉnh `_buildMenuOptions`**: Thay vì chỉ cập nhật `_selectedMenuIndex`, gọi hàm hiển thị bottom sheet với nội dung tương ứng.

Dưới đây là các bước triển khai chi tiết.

---

### Điều chỉnh mã trong `home_screen.dart`

#### 1. Cập nhật cấu trúc chính của `HomeScreen`
Loại bỏ phần `Expanded` trong `Column` để giao diện mặc định chỉ gồm danh sách dự án và menu, giống trang 1 của `sceen_mau.pdf`.

```dart
@override
Widget build(BuildContext context) {
  return Scaffold(
    backgroundColor: Colors.transparent,
    body: SafeArea(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          SizedBox(height: 20),
          _buildProjectList(),
          SizedBox(height: 20),
          _buildPageIndicator(),
          SizedBox(height: 20),
          _buildMenuOptions(),
          // Loại bỏ SizedBox và Expanded chứa _buildSelectedContent()
        ],
      ),
    ),
  );
}
```

#### 2. Điều chỉnh `_buildMenuOptions`
Thay vì cập nhật `_selectedMenuIndex` và hiển thị nội dung trực tiếp, gọi một hàm mới `_showContentBottomSheet` để hiển thị bottom sheet.

```dart
Widget _buildMenuOptions() {
  return Padding(
    padding: const EdgeInsets.symmetric(horizontal: 16),
    child: Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: AppColors.surfaceOverlay, width: 1.5),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildMenuButton(
            icon: Icons.library_books,
            label: 'Thư viện',
            onTap: () => _showContentBottomSheet(0),
          ),
          _buildMenuButton(
            icon: Icons.add,
            label: 'Thêm mới',
            onTap: () => _showContentBottomSheet(1),
          ),
          _buildMenuButton(
            icon: Icons.calculate,
            label: 'Tính khối lượng',
            onTap: () => _showContentBottomSheet(2),
          ),
        ],
      ),
    ),
  );
}
```

Lưu ý: Xóa tham số `index` trong `_buildMenuButton` vì không cần theo dõi trạng thái `_selectedMenuIndex` nữa. Cập nhật `_buildMenuButton` như sau:

```dart
Widget _buildMenuButton({
  required IconData icon,
  required String label,
  required VoidCallback onTap,
}) {
  return GestureDetector(
    onTap: onTap,
    child: Column(
      children: [
        Container(
          padding: EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.transparent,
            shape: BoxShape.circle,
          ),
          child: Icon(
            icon,
            color: AppColors.textPrimary,
            size: 26,
          ),
        ),
        SizedBox(height: 8),
        Text(
          label,
          style: TextStyle(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.w300,
            fontSize: 12,
          ),
        ),
      ],
    ),
  );
}
```

#### 3. Thêm hàm `_showContentBottomSheet`
Hàm này hiển thị một `ModalBottomSheet` chứa `DraggableScrollableSheet` để người dùng có thể vuốt lên/xuống.

```dart
void _showContentBottomSheet(int index) {
  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    builder: (context) => DraggableScrollableSheet(
      initialChildSize: 0.6, // Kích thước ban đầu chiếm 60% màn hình
      minChildSize: 0.1,     // Kích thước tối thiểu khi vuốt xuống
      maxChildSize: 0.9,     // Kích thước tối đa khi vuốt lên
      builder: (_, controller) => Container(
        decoration: BoxDecoration(
          color: AppColors.getSurfaceOverlay(context),
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: ListView(
          controller: controller,
          children: [
            // Thanh kéo (handle bar)
            Center(
              child: Container(
                margin: EdgeInsets.symmetric(vertical: 8),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: AppColors.textSubtle,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
            ),
            // Nội dung dựa trên index
            _buildSelectedContent(index),
          ],
        ),
      ),
    ),
  );
}
```

#### 4. Cập nhật `_buildSelectedContent`
Thêm tham số `index` để hàm trả về nội dung phù hợp dựa trên nút được nhấn.

```dart
Widget _buildSelectedContent(int index) {
  switch (index) {
    case 0:
      return _buildMaterialLibraryContent();
    case 1:
      return _buildAddNewContent();
    case 2:
      return _buildCalculateVolumeContent();
    default:
      return _buildMaterialLibraryContent();
  }
}
```

#### 5. Đảm bảo các hàm nội dung hoạt động tốt trong bottom sheet
Các hàm `_buildMaterialLibraryContent()`, `_buildAddNewContent()`, và `_buildCalculateVolumeContent()` không cần thay đổi nhiều, nhưng cần đảm bảo chúng hoạt động tốt trong `ListView` của bottom sheet. Nếu nội dung quá dài, `ListView` sẽ tự động cuộn.

---

### Kết quả sau khi tối ưu hóa

- **Giao diện mặc định**:
  - Chỉ hiển thị danh sách dự án (`_buildProjectList`) và 3 nút menu (`_buildMenuOptions`), giống trang 1 của `sceen_mau.pdf`.
  - Không có nội dung widget nào bên dưới menu khi chưa nhấn nút.

- **Khi nhấn nút menu**:
  - Một bottom sheet xuất hiện từ dưới màn hình, chứa nội dung tương ứng (Thư viện, Thêm mới, hoặc Tính khối lượng).
  - Người dùng có thể vuốt lên để mở rộng, vuốt xuống để thu gọn hoặc ẩn hoàn toàn, giống trang 2 của `sceen_mau.pdf`.

- **Trải nghiệm người dùng**:
  - Giao diện gọn gàng hơn, tập trung vào danh sách dự án khi mở ứng dụng.
  - Tính năng vuốt xuống để ẩn nội dung mang lại sự linh hoạt và đúng với thiết kế yêu cầu.

---

### Mã hoàn chỉnh sau điều chỉnh (phần thay đổi chính)

```dart
class _HomeScreenState extends State<HomeScreen> {
  int _currentPage = 0;
  late final PageController _pageController;

  @override
  void initState() {
    super.initState();
    _pageController = PageController(viewportFraction: 0.85);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: SafeArea(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            SizedBox(height: 20),
            _buildProjectList(),
            SizedBox(height: 20),
            _buildPageIndicator(),
            SizedBox(height: 20),
            _buildMenuOptions(),
          ],
        ),
      ),
    );
  }

  Widget _buildMenuOptions() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Container(
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.transparent,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: AppColors.surfaceOverlay, width: 1.5),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _buildMenuButton(
              icon: Icons.library_books,
              label: 'Thư viện',
              onTap: () => _showContentBottomSheet(0),
            ),
            _buildMenuButton(
              icon: Icons.add,
              label: 'Thêm mới',
              onTap: () => _showContentBottomSheet(1),
            ),
            _buildMenuButton(
              icon: Icons.calculate,
              label: 'Tính khối lượng',
              onTap: () => _showContentBottomSheet(2),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMenuButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.transparent,
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              color: AppColors.textPrimary,
              size: 26,
            ),
          ),
          SizedBox(height: 8),
          Text(
            label,
            style: TextStyle(
              color: AppColors.textPrimary,
              fontWeight: FontWeight.w300,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  void _showContentBottomSheet(int index) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.6,
        minChildSize: 0.1,
        maxChildSize: 0.9,
        builder: (_, controller) => Container(
          decoration: BoxDecoration(
            color: AppColors.getSurfaceOverlay(context),
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: ListView(
            controller: controller,
            children: [
              Center(
                child: Container(
                  margin: EdgeInsets.symmetric(vertical: 8),
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: AppColors.textSubtle,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              ),
              _buildSelectedContent(index),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSelectedContent(int index) {
    switch (index) {
      case 0:
        return _buildMaterialLibraryContent();
      case 1:
        return _buildAddNewContent();
      case 2:
        return _buildCalculateVolumeContent();
      default:
        return _buildMaterialLibraryContent();
    }
  }

  // Các hàm khác như _buildHeader(), _buildProjectList(), v.v. giữ nguyên
}
```

---

### Kết luận

Với các thay đổi trên, tệp `home_screen.dart` đã được tối ưu hóa để:
- Hiển thị giao diện mặc định như trang 1 của `sceen_mau.pdf`.
- Khi nhấn vào nút menu, nội dung xuất hiện trong một bottom sheet có thể vuốt xuống để ẩn, giống trang 2 của `sceen_mau.pdf`.
- Mã được cải thiện để linh hoạt hơn, giảm sự lộn xộn trong giao diện mặc định và tăng trải nghiệm người dùng theo đúng thiết kế yêu cầu.