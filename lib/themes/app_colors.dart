import 'package:flutter/material.dart';

/// Bảng màu ứng dụng theo thiết kế request11.md
class AppColors {
  // Card Gradient Colors (Pink to Purple - từ ảnh thẻ)
  static const Color cardPinkStart = Color(0xFFFF9EC7); // Pink sáng
  static const Color cardPurpleEnd = Color(0xFF9C7FFF); // Tím sáng

  // Background Gradient Colors (Purple to Dark - từ ảnh background)
  static const Color bgPurpleTop = Color(0xFF7758CE); // Tím sáng trên
  static const Color bgBlueMiddle1 = Color(0xFF172679); // Xanh tím
  static const Color bgBlueMiddle2 = Color(0xFF080E46); // Xanh đậm
  static const Color bgDarkBottom = Color(0xFF00000B); // Đen xanh đậm

  // Text Colors
  static const Color textPrimary = Color(0xFFFFFFFF); // Trắng
  static const Color textSecondary = Color(0xFFFFFFFF); // Trắng
  static final Color textSubtle = const Color(
    0xFFFFFFFF,
  ).withValues(alpha: 0.8); // Trắng mờ

  // Surface Colors - Light Theme
  static final Color surfaceOverlay = const Color(
    0xFFFFFFFF,
  ).withValues(alpha: 0.2); // Trắng trong suốt cho theme sáng
  static final Color iconBackground = const Color(
    0xFFFFFFFF,
  ).withValues(alpha: 0.2); // Background icon cho theme sáng

  // Surface Colors - Dark Theme
  static final Color surfaceOverlayDark = const Color(
    0xFF000000,
  ).withValues(alpha: 0.6); // Đen trong suốt cho theme tối
  static final Color iconBackgroundDark = const Color(
    0xFF000000,
  ).withValues(alpha: 0.6); // Background icon cho theme tối

  // Additional Colors for compatibility
  static const Color primaryColor = Color(0xFF7C4DFF); // Tím chính
  static const Color secondaryColor = Color(0xFFFF9EC7); // Hồng phụ
  static const Color accentColor = Color(0xFF9C7FFF); // Tím accent
  static const Color backgroundColor = Color(0xFFF5F5F5); // Background

  // Text Colors for compatibility
  static const Color textPrimaryColor = Color(0xFF2C3E50);
  static const Color textSecondaryColor = Color(0xFF7F8C8D);
  static const Color textLightColor = Color(0xFFBDC3C7);

  // Card Colors for different types
  static const Color cardBlueColor = Color(0xFF3F51B5);
  static const Color cardGreenColor = Color(0xFF4CAF50);
  static const Color cardOrangeColor = Color(0xFFFF9800);
  static const Color cardRedColor = Color(0xFFF44336);
  static const Color cardYellowColor = Color(0xFFFFC107);
  static const Color cardPurpleColor = Color(0xFF9B59B6);

  // Gradient colors for compatibility
  static const List<Color> primaryGradient = [
    Color(0xFF7C4DFF),
    Color(0xFF3F51B5),
  ];

  /// Helper methods để tự động chọn màu dựa trên theme hiện tại
  static Color getSurfaceOverlay(BuildContext context) {
    final brightness = Theme.of(context).brightness;
    return brightness == Brightness.dark ? surfaceOverlayDark : surfaceOverlay;
  }

  static Color getIconBackground(BuildContext context) {
    final brightness = Theme.of(context).brightness;
    return brightness == Brightness.dark ? iconBackgroundDark : iconBackground;
  }
}
