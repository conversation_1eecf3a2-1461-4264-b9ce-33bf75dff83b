import 'package:flutter/material.dart';
import '../themes/app_colors.dart';
import '../themes/app_gradients.dart';

/// Card với gradient theo thiết kế request11.md
class GradientCard extends StatelessWidget {
  final String title;
  final String subtitle;
  final IconData? icon;
  final VoidCallback? onTap;
  final double? width;
  final double? height;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final Gradient? gradient;
  final String? imagePath;
  final bool useColumnLayout;

  const GradientCard({
    Key? key,
    required this.title,
    required this.subtitle,
    this.icon,
    this.onTap,
    this.width = 300,
    this.height = 150,
    this.padding = const EdgeInsets.all(20),
    this.margin,
    this.gradient,
    this.imagePath,
    this.useColumnLayout = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin,
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          width: width,
          height: height,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            gradient: gradient ?? AppGradients.cardGradient,
            boxShadow: [
              BoxShadow(
                color: AppColors.bgDarkBottom.withValues(alpha: 0.3),
                blurRadius: 12,
                offset: const Offset(0, 6),
                spreadRadius: 0,
              ),
            ],
          ),
          child: useColumnLayout ? _buildColumnLayout() : _buildRowLayout(),
        ),
      ),
    );
  }

  /// Layout Row (mặc định) - icon bên phải
  Widget _buildRowLayout() {
    return Padding(
      padding: padding!,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    color: AppColors.textPrimary,
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: TextStyle(
                    color: AppColors.textSubtle,
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
          if (icon != null)
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: AppColors.surfaceOverlay,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: AppColors.textPrimary, size: 20),
            ),
        ],
      ),
    );
  }

  /// Layout Column - ảnh ở trên, thông tin ở dưới
  Widget _buildColumnLayout() {
    return Column(
      children: [
        // Ảnh dự án
        if (imagePath != null && imagePath!.isNotEmpty)
          Expanded(
            flex: 3,
            child: Container(
              width: double.infinity,
              decoration: BoxDecoration(
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
                image: DecorationImage(
                  image: AssetImage(imagePath!),
                  fit: BoxFit.cover,
                ),
              ),
            ),
          )
        else
          // Ảnh mặc định nếu không có ảnh
          Expanded(
            flex: 3,
            child: Container(
              width: double.infinity,
              decoration: BoxDecoration(
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
                image: const DecorationImage(
                  image: AssetImage('assets/images/project_placeholder.jpg'),
                  fit: BoxFit.cover,
                ),
              ),
            ),
          ),

        // Thông tin dự án
        Expanded(
          flex: 2,
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    color: AppColors.textPrimary,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: TextStyle(
                    color: AppColors.textSubtle,
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

/// Card gradient đơn giản cho list items
class SimpleGradientCard extends StatelessWidget {
  final Widget child;
  final VoidCallback? onTap;
  final EdgeInsetsGeometry? margin;
  final EdgeInsetsGeometry? padding;
  final Gradient? gradient;

  const SimpleGradientCard({
    Key? key,
    required this.child,
    this.onTap,
    this.margin = const EdgeInsets.symmetric(vertical: 4, horizontal: 0),
    this.padding = const EdgeInsets.all(16),
    this.gradient,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin,
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            gradient: gradient ?? AppGradients.cardGradient,
            boxShadow: [
              BoxShadow(
                color: AppColors.bgDarkBottom.withValues(alpha: 0.2),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Padding(padding: padding!, child: child),
        ),
      ),
    );
  }
}
