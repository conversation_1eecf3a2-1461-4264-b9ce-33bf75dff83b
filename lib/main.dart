import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import 'models/material_provider.dart';
import 'models/project/project_provider.dart';
import 'screens/project_list_screen.dart';
import 'services/price_storage_service.dart';
import 'theme/construction_theme.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Khởi tạo material provider
  final materialProvider = MaterialProvider();

  // Tải giá đã lưu
  final priceStorageService = PriceStorageService();
  final savedPrices = await priceStorageService.loadAllPrices();

  // Cập nhật giá nếu có bất kỳ giá nào đã được lưu
  if (savedPrices.isNotEmpty) {
    materialProvider.updateAllPrices(savedPrices);
  }

  runApp(MyApp(materialProvider: materialProvider));
}

class MyApp extends StatelessWidget {
  final MaterialProvider materialProvider;

  const MyApp({super.key, required this.materialProvider});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider.value(value: materialProvider),
        ChangeNotifierProvider(create: (_) => ProjectProvider()),
      ],
      child: MaterialApp(
        title: 'Dự toán vật tư xây dựng',
        theme: ConstructionTheme.lightTheme,
        darkTheme: ConstructionTheme.darkTheme,
        themeMode: ThemeMode.system, // Luôn sử dụng theme theo cài đặt hệ thống
        home: const ProjectListScreen(),
      ),
    );
  }
}
